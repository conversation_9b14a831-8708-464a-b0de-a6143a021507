canvas {
  position: absolute;
  top: 0;
  left: 0;
}
p {
  margin: 0 0;
  position: absolute;
  font: 16px Verdana;
  color: #eee;
  height: 25px;
  top: calc(100vh - 30px);
  text-shadow: 0 0 2px white;
}
p a {
  text-decoration: none;
  color: #aaa;
}
span {
  font-size: 11px;
}
p > a:first-of-type {
  font-size: 20px;
}
body {
  overflow: hidden;
}
@media (max-width: 350px) {
  p {
    font-size: 12px;
  }
  span {
    font-size: 10px;
  }
  p > a:first-of-type {
    font-size: 16px;
  }
  canvas {
    width: 100vh;
    height: 100vw;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute; 
    padding-right: 10px;
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    margin-right: 10px;
    margin-left: 10px;
    margin-top: 10px;
    margin-bottom: 10px;  
  }

}