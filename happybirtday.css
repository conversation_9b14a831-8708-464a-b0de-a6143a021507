/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  overflow: hidden;
  font-family: Verdana, sans-serif;
  background: #111;
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: block;
}

/* Responsive text elements */
p {
  margin: 0;
  position: absolute;
  font-size: clamp(12px, 2vw, 16px);
  color: #eee;
  height: auto;
  bottom: 10px;
  left: 10px;
  right: 10px;
  text-shadow: 0 0 2px white;
  z-index: 10;
}

p a {
  text-decoration: none;
  color: #aaa;
  transition: color 0.3s ease;
}

p a:hover {
  color: #fff;
}

span {
  font-size: clamp(9px, 1.5vw, 11px);
}

p > a:first-of-type {
  font-size: clamp(16px, 3vw, 20px);
}

/* Media queries for different screen sizes */
@media screen and (max-width: 768px) {
  p {
    font-size: clamp(10px, 3vw, 14px);
    bottom: 5px;
    left: 5px;
    right: 5px;
  }

  span {
    font-size: clamp(8px, 2vw, 10px);
  }

  p > a:first-of-type {
    font-size: clamp(14px, 4vw, 18px);
  }
}

@media screen and (max-width: 480px) {
  p {
    font-size: clamp(8px, 4vw, 12px);
    bottom: 3px;
    left: 3px;
    right: 3px;
  }

  span {
    font-size: clamp(7px, 3vw, 9px);
  }

  p > a:first-of-type {
    font-size: clamp(12px, 5vw, 16px);
  }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
  p {
    font-size: clamp(8px, 2vh, 12px);
  }

  span {
    font-size: clamp(6px, 1.5vh, 9px);
  }

  p > a:first-of-type {
    font-size: clamp(10px, 3vh, 14px);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  p a {
    padding: 5px;
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.3);
    margin: 2px;
    display: inline-block;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Prevent zoom on double tap for mobile */
canvas {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Performance optimizations */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  canvas {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
